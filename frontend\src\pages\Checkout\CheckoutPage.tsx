import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Form,
  Input,
  Select,
  Space,
  Typography,
  Divider,
  Radio,
  List,
  Image,
  Tag,
  Steps,
  Breadcrumb,
  message,
  Modal
} from 'antd';
import {
  HomeOutlined,
  ShoppingCartOutlined,
  EnvironmentOutlined,
  CreditCardOutlined,
  CheckCircleOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../../stores/authStore';
import { useCartStore } from '../../stores/cartStore';
import PaymentSystem from '../../components/business/PaymentSystem';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import { theme } from '../../styles/theme';
import { ordersService } from '../../services/orders';

const { Title, Text } = Typography;
const { Option } = Select;
const { Step } = Steps;

const CheckoutContainer = styled.div`
  min-height: 100vh;
  background: ${theme.colors.gradients.cool};

  .page-header {
    background: white;
    padding: ${theme.spacing[4]} 0;
    border-bottom: 1px solid ${theme.colors.gray[200]};
    box-shadow: ${theme.boxShadow.sm};
    
    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;
      
      .breadcrumb {
        margin-bottom: 16px;
      }
      
      .page-title {
        margin: 0;
        font-size: 24px;
        font-weight: 700;
      }
    }
  }
  
  .checkout-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
    
    .checkout-steps {
      background: white;
      padding: 24px;
      border-radius: 12px;
      margin-bottom: 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    .checkout-main {
      display: flex;
      gap: 24px;
      
      @media (max-width: 768px) {
        flex-direction: column;
      }
      
      .checkout-left {
        flex: 1;
        
        .section-card {
          background: white;
          border-radius: 12px;
          margin-bottom: 16px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          
          .section-header {
            padding: 20px 24px 0 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            
            .section-title {
              font-size: 18px;
              font-weight: 600;
              display: flex;
              align-items: center;
              gap: 8px;
            }
            
            .section-action {
              color: #1677ff;
              cursor: pointer;
              font-size: 14px;
              
              &:hover {
                color: #0958d9;
              }
            }
          }
          
          .section-content {
            padding: 16px 24px 24px 24px;
          }
        }
        
        .address-section {
          .address-list {
            .address-item {
              padding: 16px;
              border: 1px solid #f0f0f0;
              border-radius: 8px;
              margin-bottom: 12px;
              cursor: pointer;
              transition: all 0.3s ease;
              
              &:hover {
                border-color: #1677ff;
              }
              
              &.selected {
                border-color: #1677ff;
                background: #f6f9ff;
              }
              
              .address-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
                
                .address-info {
                  .recipient-name {
                    font-weight: 600;
                    margin-right: 12px;
                  }
                  
                  .recipient-phone {
                    color: #8c8c8c;
                  }
                }
                
                .address-actions {
                  display: flex;
                  gap: 8px;
                  
                  .action-btn {
                    padding: 4px 8px;
                    border: none;
                    background: none;
                    color: #8c8c8c;
                    cursor: pointer;
                    border-radius: 4px;
                    
                    &:hover {
                      background: #f5f5f5;
                      color: #1677ff;
                    }
                  }
                }
              }
              
              .address-detail {
                color: #595959;
                line-height: 1.5;
              }
              
              .address-tags {
                margin-top: 8px;
                
                .ant-tag {
                  font-size: 11px;
                }
              }
            }
            
            .add-address-btn {
              width: 100%;
              height: 60px;
              border: 1px dashed #d9d9d9;
              background: #fafafa;
              border-radius: 8px;
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 8px;
              color: #8c8c8c;
              cursor: pointer;
              transition: all 0.3s ease;
              
              &:hover {
                border-color: #1677ff;
                color: #1677ff;
                background: #f6f9ff;
              }
            }
          }
        }
        
        .items-section {
          .items-list {
            .item-row {
              display: flex;
              align-items: center;
              padding: 16px 0;
              border-bottom: 1px solid #f0f0f0;
              
              &:last-child {
                border-bottom: none;
              }
              
              .item-image {
                width: 80px;
                height: 100px;
                border-radius: 6px;
                overflow: hidden;
                margin-right: 16px;
                
                img {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                }
              }
              
              .item-info {
                flex: 1;
                
                .item-title {
                  font-size: 16px;
                  font-weight: 600;
                  margin-bottom: 8px;
                  line-height: 1.4;
                }
                
                .item-meta {
                  display: flex;
                  align-items: center;
                  gap: 12px;
                  margin-bottom: 8px;
                  
                  .meta-item {
                    font-size: 13px;
                    color: #8c8c8c;
                  }
                }
                
                .item-price {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  
                  .price-info {
                    .current-price {
                      font-size: 18px;
                      font-weight: 700;
                      color: #ff4d4f;
                    }
                    
                    .original-price {
                      font-size: 14px;
                      color: #8c8c8c;
                      text-decoration: line-through;
                      margin-left: 8px;
                    }
                  }
                  
                  .quantity-info {
                    color: #8c8c8c;
                    font-size: 14px;
                  }
                }
              }
            }
          }
        }
      }
      
      .checkout-right {
        width: 360px;
        
        @media (max-width: 768px) {
          width: 100%;
        }
        
        .summary-card {
          background: white;
          border-radius: 12px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          position: sticky;
          top: 24px;
          
          .summary-header {
            padding: 20px 24px 0 24px;
            
            .summary-title {
              font-size: 18px;
              font-weight: 600;
            }
          }
          
          .summary-content {
            padding: 16px 24px 24px 24px;
            
            .summary-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 12px;
              
              .item-label {
                color: #595959;
              }
              
              .item-value {
                font-weight: 500;
                
                &.highlight {
                  color: #ff4d4f;
                  font-size: 18px;
                  font-weight: 700;
                }
                
                &.discount {
                  color: #52c41a;
                }
              }
            }
            
            .summary-divider {
              margin: 16px 0;
            }
            
            .submit-btn {
              width: 100%;
              height: 48px;
              font-size: 16px;
              font-weight: 600;
              border-radius: 8px;
              background: linear-gradient(135deg, #1677ff, #4096ff);
              border: none;
              
              &:hover {
                background: linear-gradient(135deg, #0958d9, #1677ff);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
              }
              
              &:disabled {
                background: #f5f5f5;
                color: #bfbfbf;
                transform: none;
                box-shadow: none;
              }
            }
          }
        }
      }
    }
  }
`;

interface CheckoutPageProps {}

const CheckoutPage: React.FC<CheckoutPageProps> = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuthStore();
  const { clearCart } = useCartStore();
  
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedAddress, setSelectedAddress] = useState<any>(null);
  const [addresses, setAddresses] = useState<any[]>([]);
  const [checkoutItems, setCheckoutItems] = useState<any[]>([]);
  const [coupon, setCoupon] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [paymentVisible, setPaymentVisible] = useState(false);
  const [orderData, setOrderData] = useState<any>(null);

  useEffect(() => {
    // 从路由状态获取结算商品
    const items = location.state?.items || [];
    const appliedCoupon = location.state?.coupon;
    
    if (items.length === 0) {
      message.warning('没有选择商品');
      navigate('/cart');
      return;
    }
    
    setCheckoutItems(items);
    setCoupon(appliedCoupon);
    
    // 加载用户地址
    loadAddresses();
  }, [location.state, navigate]);

  const loadAddresses = async () => {
    // 模拟加载地址数据
    const mockAddresses = [
      {
        id: '1',
        recipient_name: '张三',
        recipient_phone: '13800138000',
        province: '北京市',
        city: '北京市',
        district: '朝阳区',
        detail: '三里屯街道工体北路8号院',
        is_default: true,
        tags: ['家']
      },
      {
        id: '2',
        recipient_name: '张三',
        recipient_phone: '13800138000',
        province: '上海市',
        city: '上海市',
        district: '浦东新区',
        detail: '陆家嘴金融中心',
        is_default: false,
        tags: ['公司']
      }
    ];
    
    setAddresses(mockAddresses);
    setSelectedAddress(mockAddresses.find(addr => addr.is_default) || mockAddresses[0]);
  };

  const calculateTotal = () => {
    const subtotal = checkoutItems.reduce((total, item) => total + item.price * item.quantity, 0);
    const discount = coupon ? (coupon.type === 'percentage' ? subtotal * coupon.discount / 100 : coupon.discount) : 0;
    const shipping = subtotal >= 99 ? 0 : 10;
    
    return {
      subtotal,
      discount,
      shipping,
      total: subtotal - discount + shipping
    };
  };

  const handleSubmitOrder = async () => {
    if (!selectedAddress) {
      message.warning('请选择收货地址');
      return;
    }

    try {
      setLoading(true);
      
      const orderData = {
        items: checkoutItems.map(item => ({
          book_id: item.id,
          quantity: item.quantity
        })),
        delivery_address: `${selectedAddress.province} ${selectedAddress.city} ${selectedAddress.district} ${selectedAddress.detail}`,
        delivery_phone: selectedAddress.recipient_phone
      };

      const response = await ordersService.createOrder(orderData);
      
      if (response.success && response.data) {
        setOrderData({
          id: response.data.id,
          total_amount: response.data.total_amount,
          items: checkoutItems,
          shipping_fee: calculateTotal().shipping,
          discount: calculateTotal().discount
        });
        setPaymentVisible(true);
        setCurrentStep(1);
      }
    } catch (error) {
      message.error('创建订单失败');
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentSuccess = (paymentResult: any) => {
    setCurrentStep(2);
    setPaymentVisible(false);
    
    // 清空购物车中的已购买商品
    checkoutItems.forEach(item => {
      clearCart();
    });
    
    message.success('支付成功！');
    
    // 跳转到订单详情
    setTimeout(() => {
      navigate(`/orders/${orderData.id}`);
    }, 2000);
  };

  const handlePaymentCancel = () => {
    setPaymentVisible(false);
    setCurrentStep(0);
  };

  const summary = calculateTotal();

  const steps = [
    {
      title: '确认订单',
      description: '确认商品和收货信息'
    },
    {
      title: '支付订单',
      description: '选择支付方式完成支付'
    },
    {
      title: '完成订单',
      description: '订单创建成功'
    }
  ];

  return (
    <CheckoutContainer>
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <Breadcrumb className="breadcrumb">
            <Breadcrumb.Item>
              <HomeOutlined />
              <span onClick={() => navigate('/')} style={{ cursor: 'pointer', marginLeft: 8 }}>
                首页
              </span>
            </Breadcrumb.Item>
            <Breadcrumb.Item>
              <ShoppingCartOutlined />
              <span onClick={() => navigate('/cart')} style={{ cursor: 'pointer', marginLeft: 8 }}>
                购物车
              </span>
            </Breadcrumb.Item>
            <Breadcrumb.Item>结算</Breadcrumb.Item>
          </Breadcrumb>
          
          <Title level={2} className="page-title">订单结算</Title>
        </div>
      </div>

      <div className="checkout-content">
        {/* 步骤条 */}
        <div className="checkout-steps">
          <Steps current={currentStep}>
            {steps.map(step => (
              <Step key={step.title} title={step.title} description={step.description} />
            ))}
          </Steps>
        </div>

        {/* 支付弹窗 */}
        <Modal
          title="支付订单"
          open={paymentVisible}
          onCancel={handlePaymentCancel}
          footer={null}
          width={800}
          destroyOnClose
        >
          {orderData && (
            <PaymentSystem
              orderData={orderData}
              onPaymentSuccess={handlePaymentSuccess}
              onPaymentCancel={handlePaymentCancel}
            />
          )}
        </Modal>

        {/* 主要内容 */}
        <div className="checkout-main">
          <div className="checkout-left">
            {/* 收货地址 */}
            <Card className="section-card">
              <div className="section-header">
                <div className="section-title">
                  <EnvironmentOutlined />
                  收货地址
                </div>
                <div className="section-action">
                  <PlusOutlined style={{ marginRight: 4 }} />
                  新增地址
                </div>
              </div>
              <div className="section-content address-section">
                <div className="address-list">
                  {addresses.map(address => (
                    <div
                      key={address.id}
                      className={`address-item ${selectedAddress?.id === address.id ? 'selected' : ''}`}
                      onClick={() => setSelectedAddress(address)}
                    >
                      <div className="address-header">
                        <div className="address-info">
                          <span className="recipient-name">{address.recipient_name}</span>
                          <span className="recipient-phone">{address.recipient_phone}</span>
                        </div>
                        <div className="address-actions">
                          <button className="action-btn">
                            <EditOutlined />
                          </button>
                          <button className="action-btn">
                            <DeleteOutlined />
                          </button>
                        </div>
                      </div>
                      <div className="address-detail">
                        {address.province} {address.city} {address.district} {address.detail}
                      </div>
                      <div className="address-tags">
                        {address.is_default && <Tag color="blue">默认</Tag>}
                        {address.tags.map((tag: string) => (
                          <Tag key={tag}>{tag}</Tag>
                        ))}
                      </div>
                    </div>
                  ))}
                  
                  <div className="add-address-btn">
                    <PlusOutlined />
                    添加新地址
                  </div>
                </div>
              </div>
            </Card>

            {/* 商品清单 */}
            <Card className="section-card">
              <div className="section-header">
                <div className="section-title">
                  <ShoppingCartOutlined />
                  商品清单 ({checkoutItems.length}件)
                </div>
              </div>
              <div className="section-content items-section">
                <div className="items-list">
                  {checkoutItems.map(item => (
                    <div key={item.id} className="item-row">
                      <div className="item-image">
                        <img src={item.cover_image || '/images/book-placeholder.png'} alt={item.title} />
                      </div>
                      <div className="item-info">
                        <div className="item-title">{item.title}</div>
                        <div className="item-meta">
                          <span className="meta-item">作者: {item.author || '未知'}</span>
                          <Tag color="green">现货</Tag>
                        </div>
                        <div className="item-price">
                          <div className="price-info">
                            <span className="current-price">¥{item.price.toFixed(2)}</span>
                            {item.original_price && item.original_price > item.price && (
                              <span className="original-price">¥{item.original_price.toFixed(2)}</span>
                            )}
                          </div>
                          <div className="quantity-info">x{item.quantity}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </Card>
          </div>

          {/* 订单摘要 */}
          <div className="checkout-right">
            <Card className="summary-card">
              <div className="summary-header">
                <Title level={4} className="summary-title">订单摘要</Title>
              </div>
              <div className="summary-content">
                <div className="summary-item">
                  <span className="item-label">商品小计 ({checkoutItems.length}件)</span>
                  <span className="item-value">¥{summary.subtotal.toFixed(2)}</span>
                </div>
                
                {summary.discount > 0 && (
                  <div className="summary-item">
                    <span className="item-label">优惠券折扣</span>
                    <span className="item-value discount">-¥{summary.discount.toFixed(2)}</span>
                  </div>
                )}
                
                <div className="summary-item">
                  <span className="item-label">运费</span>
                  <span className="item-value">
                    {summary.shipping === 0 ? '免运费' : `¥${summary.shipping.toFixed(2)}`}
                  </span>
                </div>
                
                <Divider className="summary-divider" />
                
                <div className="summary-item">
                  <span className="item-label">应付金额</span>
                  <span className="item-value highlight">¥{summary.total.toFixed(2)}</span>
                </div>
                
                <Button
                  variant="gradient"
                  gradient="primary"
                  size="large"
                  loading={loading}
                  onClick={handleSubmitOrder}
                  disabled={!selectedAddress || checkoutItems.length === 0}
                  fullWidth
                  rounded
                  elevated
                >
                  提交订单
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </CheckoutContainer>
  );
};

export default CheckoutPage;
