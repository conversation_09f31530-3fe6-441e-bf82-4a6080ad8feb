import React, { useState, useEffect } from 'react';
import { Image, Spin } from 'antd';
import {
  LeftOutlined,
  RightOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  ExpandOutlined,
  RotateLeftOutlined,
  RotateRightOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { theme } from '../../styles/theme';

interface ImageItem {
  url: string;
  alt?: string;
  thumbnail?: string;
}

interface ImageGalleryProps {
  images: ImageItem[];
  initialIndex?: number;
  width?: number | string;
  height?: number | string;
  thumbnailSize?: number;
  showThumbnails?: boolean;
  showControls?: boolean;
  showZoom?: boolean;
  showExpand?: boolean;
  showRotate?: boolean;
  loading?: boolean;
}

const GalleryContainer = styled.div<{ width?: number | string; height?: number | string }>`
  width: ${({ width }) => (typeof width === 'number' ? `${width}px` : width || '100%')};
  height: ${({ height }) => (typeof height === 'number' ? `${height}px` : height || 'auto')};
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing[4]};
  position: relative;
`;

const MainImageContainer = styled.div`
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: ${theme.borderRadius['2xl']};
  overflow: hidden;
  background: ${theme.colors.gray[50]};
  box-shadow: ${theme.boxShadow.lg};
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
    z-index: 1;
    pointer-events: none;
  }
  
  .ant-image {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    
    img {
      object-fit: contain;
      max-height: 100%;
      max-width: 100%;
      transition: transform 0.3s ease;
    }
  }
  
  .loading-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.8);
    z-index: 2;
  }
`;

const ThumbnailsContainer = styled.div`
  display: flex;
  gap: ${theme.spacing[2]};
  overflow-x: auto;
  padding: ${theme.spacing[1]} 0;
  
  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    height: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background: ${theme.colors.gray[100]};
    border-radius: ${theme.borderRadius.full};
  }
  
  &::-webkit-scrollbar-thumb {
    background: ${theme.colors.gray[300]};
    border-radius: ${theme.borderRadius.full};
    
    &:hover {
      background: ${theme.colors.gray[400]};
    }
  }
`;

const ThumbnailItem = styled.div<{ active: boolean; size: number }>`
  width: ${({ size }) => size}px;
  height: ${({ size }) => Math.floor(size * 1.25)}px;
  border-radius: ${theme.borderRadius.lg};
  overflow: hidden;
  cursor: pointer;
  border: 3px solid ${({ active }) => (active ? theme.colors.primary[500] : 'transparent')};
  transition: all ${theme.animation.duration.base};
  flex-shrink: 0;
  position: relative;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: ${theme.boxShadow.md};
  }
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const ControlsContainer = styled.div`
  position: absolute;
  bottom: ${theme.spacing[4]};
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: ${theme.spacing[2]};
  background: rgba(0, 0, 0, 0.5);
  padding: ${theme.spacing[2]} ${theme.spacing[4]};
  border-radius: ${theme.borderRadius.full};
  z-index: 3;
`;

const ControlButton = styled.button`
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all ${theme.animation.duration.fast};
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
  }
  
  &:active {
    transform: scale(0.95);
  }
`;

const NavigationButton = styled.button<{ direction: 'left' | 'right' }>`
  position: absolute;
  top: 50%;
  ${({ direction }) => (direction === 'left' ? 'left: 10px;' : 'right: 10px;')}
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.3);
  border: none;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all ${theme.animation.duration.fast};
  z-index: 3;
  opacity: 0;
  
  ${MainImageContainer}:hover & {
    opacity: 1;
  }
  
  &:hover {
    background: rgba(0, 0, 0, 0.5);
    transform: translateY(-50%) scale(1.1);
  }
  
  &:active {
    transform: translateY(-50%) scale(0.95);
  }
`;

const ImageGallery: React.FC<ImageGalleryProps> = ({
  images,
  initialIndex = 0,
  width,
  height = 450,
  thumbnailSize = 70,
  showThumbnails = true,
  showControls = true,
  showZoom = true,
  showExpand = true,
  showRotate = true,
  loading = false
}) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [visible, setVisible] = useState(false);
  
  useEffect(() => {
    // Reset zoom and rotation when changing images
    setZoomLevel(1);
    setRotation(0);
  }, [currentIndex]);
  
  const handlePrev = () => {
    setCurrentIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1));
  };
  
  const handleNext = () => {
    setCurrentIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1));
  };
  
  const handleZoomIn = () => {
    setZoomLevel((prev) => Math.min(prev + 0.25, 3));
  };
  
  const handleZoomOut = () => {
    setZoomLevel((prev) => Math.max(prev - 0.25, 0.5));
  };
  
  const handleRotateLeft = () => {
    setRotation((prev) => prev - 90);
  };
  
  const handleRotateRight = () => {
    setRotation((prev) => prev + 90);
  };
  
  const handleExpand = () => {
    setVisible(true);
  };
  
  const currentImage = images[currentIndex] || { url: '' };
  
  return (
    <GalleryContainer width={width} height={height}>
      <MainImageContainer>
        {loading ? (
          <div className="loading-container">
            <Spin size="large" />
          </div>
        ) : (
          <>
            <Image
              src={currentImage.url}
              alt={currentImage.alt || `Image ${currentIndex + 1}`}
              preview={{ visible: false }}
              style={{
                transform: `scale(${zoomLevel}) rotate(${rotation}deg)`,
                transition: 'transform 0.3s ease'
              }}
              onClick={handleExpand}
            />
            
            <div style={{ display: 'none' }}>
              <Image.PreviewGroup
                preview={{
                  visible,
                  onVisibleChange: (vis) => setVisible(vis),
                  current: currentIndex
                }}
              >
                {images.map((image, index) => (
                  <Image key={index} src={image.url} alt={image.alt || `Image ${index + 1}`} />
                ))}
              </Image.PreviewGroup>
            </div>
            
            {images.length > 1 && (
              <>
                <NavigationButton direction="left" onClick={handlePrev}>
                  <LeftOutlined />
                </NavigationButton>
                <NavigationButton direction="right" onClick={handleNext}>
                  <RightOutlined />
                </NavigationButton>
              </>
            )}
            
            {showControls && (
              <ControlsContainer>
                {showZoom && (
                  <>
                    <ControlButton onClick={handleZoomOut} title="缩小">
                      <ZoomOutOutlined />
                    </ControlButton>
                    <ControlButton onClick={handleZoomIn} title="放大">
                      <ZoomInOutlined />
                    </ControlButton>
                  </>
                )}
                
                {showRotate && (
                  <>
                    <ControlButton onClick={handleRotateLeft} title="向左旋转">
                      <RotateLeftOutlined />
                    </ControlButton>
                    <ControlButton onClick={handleRotateRight} title="向右旋转">
                      <RotateRightOutlined />
                    </ControlButton>
                  </>
                )}
                
                {showExpand && (
                  <ControlButton onClick={handleExpand} title="全屏查看">
                    <ExpandOutlined />
                  </ControlButton>
                )}
              </ControlsContainer>
            )}
          </>
        )}
      </MainImageContainer>
      
      {showThumbnails && images.length > 1 && (
        <ThumbnailsContainer>
          {images.map((image, index) => (
            <ThumbnailItem
              key={index}
              active={index === currentIndex}
              size={thumbnailSize}
              onClick={() => setCurrentIndex(index)}
            >
              <img
                src={image.thumbnail || image.url}
                alt={`Thumbnail ${index + 1}`}
                loading="lazy"
              />
            </ThumbnailItem>
          ))}
        </ThumbnailsContainer>
      )}
    </GalleryContainer>
  );
};

export default ImageGallery;
