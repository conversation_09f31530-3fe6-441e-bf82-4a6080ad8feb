import React, { useState, useEffect } from 'react';
import {
  Collapse,
  Slider,
  Checkbox,
  Radio,
  Rate,
  InputNumber,
  Space,
  Tag,
  Typography,
  Divider,
  Badge
} from 'antd';
import {
  FilterOutlined,
  ClearOutlined,
  BookOutlined,
  UserOutlined,
  CalendarOutlined,
  StarOutlined,
  DollarOutlined,
  TagOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import Button from '../ui/Button';
import Card from '../ui/Card';
import { theme } from '../../styles/theme';

const { Panel } = Collapse;
const { Text } = Typography;

interface FilterOption {
  label: string;
  value: string | number;
  count?: number;
}

interface EnhancedFiltersProps {
  categories: FilterOption[];
  authors: FilterOption[];
  publishers: FilterOption[];
  conditions: FilterOption[];
  onFiltersChange: (filters: any) => void;
  onClearFilters: () => void;
  activeFilters: any;
  loading?: boolean;
}

const FiltersContainer = styled.div`
  .filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: ${theme.spacing[6]};
    padding-bottom: ${theme.spacing[4]};
    border-bottom: 2px solid ${theme.colors.gray[200]};
    
    .filters-title {
      font-size: ${theme.typography.fontSize.xl};
      font-weight: ${theme.typography.fontWeight.bold};
      color: ${theme.colors.gray[800]};
      display: flex;
      align-items: center;
      gap: ${theme.spacing[2]};
      
      .filter-icon {
        color: ${theme.colors.primary[500]};
        font-size: ${theme.typography.fontSize.lg};
      }
    }
    
    .clear-filters {
      color: ${theme.colors.gray[500]};
      cursor: pointer;
      transition: all ${theme.animation.duration.fast};
      display: flex;
      align-items: center;
      gap: ${theme.spacing[1]};
      padding: ${theme.spacing[1]} ${theme.spacing[2]};
      border-radius: ${theme.borderRadius.md};
      
      &:hover {
        color: ${theme.colors.error[500]};
        background: ${theme.colors.error[50]};
      }
    }
  }

  .active-filters {
    margin-bottom: ${theme.spacing[6]};
    
    .active-filters-title {
      font-size: ${theme.typography.fontSize.sm};
      font-weight: ${theme.typography.fontWeight.semibold};
      color: ${theme.colors.gray[700]};
      margin-bottom: ${theme.spacing[3]};
    }
    
    .filter-tags {
      display: flex;
      flex-wrap: wrap;
      gap: ${theme.spacing[2]};
      
      .filter-tag {
        background: ${theme.colors.primary[50]};
        border: 1px solid ${theme.colors.primary[200]};
        color: ${theme.colors.primary[700]};
        border-radius: ${theme.borderRadius.full};
        padding: ${theme.spacing[1]} ${theme.spacing[3]};
        font-size: ${theme.typography.fontSize.sm};
        display: flex;
        align-items: center;
        gap: ${theme.spacing[1]};
        
        .remove-icon {
          cursor: pointer;
          opacity: 0.7;
          transition: opacity ${theme.animation.duration.fast};
          
          &:hover {
            opacity: 1;
          }
        }
      }
    }
  }

  .ant-collapse {
    background: transparent;
    border: none;
    
    .ant-collapse-item {
      border: none;
      margin-bottom: ${theme.spacing[4]};
      background: ${theme.colors.gray[50]};
      border-radius: ${theme.borderRadius.lg};
      overflow: hidden;
      
      .ant-collapse-header {
        background: ${theme.colors.gray[100]};
        border-radius: ${theme.borderRadius.lg} ${theme.borderRadius.lg} 0 0;
        font-weight: ${theme.typography.fontWeight.semibold};
        color: ${theme.colors.gray[700]};
        padding: ${theme.spacing[4]} ${theme.spacing[5]};
        
        .ant-collapse-arrow {
          color: ${theme.colors.primary[500]};
        }
      }
      
      .ant-collapse-content {
        border-top: 1px solid ${theme.colors.gray[200]};
        
        .ant-collapse-content-box {
          padding: ${theme.spacing[5]};
        }
      }
    }
  }

  .filter-options {
    .option-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: ${theme.spacing[2]} 0;
      cursor: pointer;
      border-radius: ${theme.borderRadius.md};
      padding: ${theme.spacing[2]} ${theme.spacing[3]};
      margin-bottom: ${theme.spacing[1]};
      transition: all ${theme.animation.duration.fast};
      
      &:hover {
        background: ${theme.colors.primary[50]};
      }
      
      &.active {
        background: ${theme.colors.primary[100]};
        color: ${theme.colors.primary[700]};
      }
      
      .option-label {
        flex: 1;
        font-weight: ${theme.typography.fontWeight.medium};
      }
      
      .option-count {
        background: ${theme.colors.gray[200]};
        color: ${theme.colors.gray[600]};
        padding: ${theme.spacing[1]} ${theme.spacing[2]};
        border-radius: ${theme.borderRadius.full};
        font-size: ${theme.typography.fontSize.xs};
        font-weight: ${theme.typography.fontWeight.semibold};
      }
    }
  }

  .price-range {
    .price-inputs {
      display: flex;
      gap: ${theme.spacing[2]};
      align-items: center;
      margin-top: ${theme.spacing[4]};
      
      .price-input {
        flex: 1;
        
        .ant-input-number {
          width: 100%;
          border-radius: ${theme.borderRadius.md};
        }
      }
      
      .price-separator {
        color: ${theme.colors.gray[500]};
        font-weight: ${theme.typography.fontWeight.semibold};
      }
    }
    
    .ant-slider {
      margin: ${theme.spacing[6]} 0 ${theme.spacing[4]} 0;
      
      .ant-slider-rail {
        background: ${theme.colors.gray[200]};
        height: 6px;
      }
      
      .ant-slider-track {
        background: ${theme.colors.gradients.primary};
        height: 6px;
      }
      
      .ant-slider-handle {
        border: 3px solid ${theme.colors.primary[500]};
        width: 20px;
        height: 20px;
        margin-top: -7px;
        
        &:hover {
          border-color: ${theme.colors.primary[600]};
        }
      }
    }
  }

  .rating-filter {
    .rating-options {
      display: flex;
      flex-direction: column;
      gap: ${theme.spacing[2]};
      
      .rating-option {
        display: flex;
        align-items: center;
        gap: ${theme.spacing[2]};
        padding: ${theme.spacing[2]};
        border-radius: ${theme.borderRadius.md};
        cursor: pointer;
        transition: background ${theme.animation.duration.fast};
        
        &:hover {
          background: ${theme.colors.gray[50]};
        }
        
        &.active {
          background: ${theme.colors.primary[50]};
        }
        
        .ant-rate {
          font-size: ${theme.typography.fontSize.sm};
        }
        
        .rating-text {
          font-size: ${theme.typography.fontSize.sm};
          color: ${theme.colors.gray[600]};
        }
      }
    }
  }
`;

const EnhancedFilters: React.FC<EnhancedFiltersProps> = ({
  categories,
  authors,
  publishers,
  conditions,
  onFiltersChange,
  onClearFilters,
  activeFilters,
  loading = false
}) => {
  const [localFilters, setLocalFilters] = useState(activeFilters);
  const [priceRange, setPriceRange] = useState([0, 1000]);

  useEffect(() => {
    setLocalFilters(activeFilters);
  }, [activeFilters]);

  const handleFilterChange = (key: string, value: any) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const removeFilter = (key: string) => {
    const newFilters = { ...localFilters };
    delete newFilters[key];
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const getActiveFilterTags = () => {
    const tags = [];
    
    if (localFilters.category) {
      const category = categories.find(c => c.value === localFilters.category);
      tags.push({ key: 'category', label: `分类: ${category?.label}` });
    }
    
    if (localFilters.author) {
      tags.push({ key: 'author', label: `作者: ${localFilters.author}` });
    }
    
    if (localFilters.publisher) {
      tags.push({ key: 'publisher', label: `出版社: ${localFilters.publisher}` });
    }
    
    if (localFilters.condition) {
      const condition = conditions.find(c => c.value === localFilters.condition);
      tags.push({ key: 'condition', label: `品相: ${condition?.label}` });
    }
    
    if (localFilters.minPrice || localFilters.maxPrice) {
      tags.push({ 
        key: 'price', 
        label: `价格: ¥${localFilters.minPrice || 0} - ¥${localFilters.maxPrice || 1000}` 
      });
    }
    
    if (localFilters.rating) {
      tags.push({ key: 'rating', label: `评分: ${localFilters.rating}星以上` });
    }
    
    return tags;
  };

  const activeFilterTags = getActiveFilterTags();

  return (
    <FiltersContainer>
      <div className="filters-header">
        <div className="filters-title">
          <FilterOutlined className="filter-icon" />
          筛选条件
          {activeFilterTags.length > 0 && (
            <Badge count={activeFilterTags.length} style={{ marginLeft: 8 }} />
          )}
        </div>
        <div className="clear-filters" onClick={onClearFilters}>
          <ClearOutlined />
          清空
        </div>
      </div>

      {activeFilterTags.length > 0 && (
        <div className="active-filters">
          <div className="active-filters-title">已选条件</div>
          <div className="filter-tags">
            {activeFilterTags.map(tag => (
              <div key={tag.key} className="filter-tag">
                {tag.label}
                <ClearOutlined 
                  className="remove-icon" 
                  onClick={() => removeFilter(tag.key)}
                />
              </div>
            ))}
          </div>
        </div>
      )}

      <Collapse defaultActiveKey={['category', 'price']} ghost>
        <Panel 
          header={
            <Space>
              <BookOutlined />
              图书分类
            </Space>
          } 
          key="category"
        >
          <div className="filter-options">
            {categories.map(category => (
              <div
                key={category.value}
                className={`option-item ${localFilters.category === category.value ? 'active' : ''}`}
                onClick={() => handleFilterChange('category', category.value)}
              >
                <span className="option-label">{category.label}</span>
                {category.count && (
                  <span className="option-count">{category.count}</span>
                )}
              </div>
            ))}
          </div>
        </Panel>

        <Panel 
          header={
            <Space>
              <DollarOutlined />
              价格范围
            </Space>
          } 
          key="price"
        >
          <div className="price-range">
            <Slider
              range
              min={0}
              max={1000}
              value={[localFilters.minPrice || 0, localFilters.maxPrice || 1000]}
              onChange={(value) => {
                handleFilterChange('minPrice', value[0]);
                handleFilterChange('maxPrice', value[1]);
              }}
              tooltip={{ formatter: (value) => `¥${value}` }}
            />
            <div className="price-inputs">
              <div className="price-input">
                <InputNumber
                  min={0}
                  max={1000}
                  value={localFilters.minPrice}
                  onChange={(value) => handleFilterChange('minPrice', value)}
                  placeholder="最低价"
                  prefix="¥"
                />
              </div>
              <span className="price-separator">-</span>
              <div className="price-input">
                <InputNumber
                  min={0}
                  max={1000}
                  value={localFilters.maxPrice}
                  onChange={(value) => handleFilterChange('maxPrice', value)}
                  placeholder="最高价"
                  prefix="¥"
                />
              </div>
            </div>
          </div>
        </Panel>

        <Panel 
          header={
            <Space>
              <UserOutlined />
              作者
            </Space>
          } 
          key="author"
        >
          <div className="filter-options">
            {authors.slice(0, 10).map(author => (
              <div
                key={author.value}
                className={`option-item ${localFilters.author === author.value ? 'active' : ''}`}
                onClick={() => handleFilterChange('author', author.value)}
              >
                <span className="option-label">{author.label}</span>
                {author.count && (
                  <span className="option-count">{author.count}</span>
                )}
              </div>
            ))}
          </div>
        </Panel>

        <Panel 
          header={
            <Space>
              <StarOutlined />
              评分
            </Space>
          } 
          key="rating"
        >
          <div className="rating-filter">
            <div className="rating-options">
              {[5, 4, 3, 2, 1].map(rating => (
                <div
                  key={rating}
                  className={`rating-option ${localFilters.rating === rating ? 'active' : ''}`}
                  onClick={() => handleFilterChange('rating', rating)}
                >
                  <Rate disabled defaultValue={rating} />
                  <span className="rating-text">{rating}星以上</span>
                </div>
              ))}
            </div>
          </div>
        </Panel>

        <Panel 
          header={
            <Space>
              <TagOutlined />
              图书品相
            </Space>
          } 
          key="condition"
        >
          <div className="filter-options">
            {conditions.map(condition => (
              <div
                key={condition.value}
                className={`option-item ${localFilters.condition === condition.value ? 'active' : ''}`}
                onClick={() => handleFilterChange('condition', condition.value)}
              >
                <span className="option-label">{condition.label}</span>
                {condition.count && (
                  <span className="option-count">{condition.count}</span>
                )}
              </div>
            ))}
          </div>
        </Panel>
      </Collapse>
    </FiltersContainer>
  );
};

export default EnhancedFilters;
