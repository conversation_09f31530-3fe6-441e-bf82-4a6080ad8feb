import React from 'react';
import { Empty, Typography } from 'antd';
import {
  BookOutlined,
  SearchOutlined,
  InboxOutlined,
  FileSearchOutlined,
  ShoppingOutlined,
  HeartOutlined,
  UserOutlined,
  WarningOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import Button from './Button';
import { theme } from '../../styles/theme';

const { Text } = Typography;

export type EmptyStateVariant = 
  | 'no-books' 
  | 'no-search-results' 
  | 'no-data' 
  | 'no-favorites' 
  | 'no-orders' 
  | 'no-cart' 
  | 'no-users'
  | 'error';

interface EmptyStateProps {
  variant?: EmptyStateVariant;
  title?: string;
  description?: string;
  actionText?: string;
  onAction?: () => void;
  showAction?: boolean;
  size?: 'small' | 'medium' | 'large';
}

const EmptyContainer = styled.div<{ size: 'small' | 'medium' | 'large' }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: ${({ size }) => {
    switch (size) {
      case 'small':
        return `${theme.spacing[8]} ${theme.spacing[4]}`;
      case 'large':
        return `${theme.spacing[20]} ${theme.spacing[8]}`;
      default:
        return `${theme.spacing[16]} ${theme.spacing[6]}`;
    }
  }};
  
  .empty-icon {
    font-size: ${({ size }) => {
      switch (size) {
        case 'small':
          return theme.typography.fontSize['4xl'];
        case 'large':
          return theme.typography.fontSize['6xl'];
        default:
          return theme.typography.fontSize['5xl'];
      }
    }};
    color: ${theme.colors.gray[300]};
    margin-bottom: ${theme.spacing[6]};
    opacity: 0.8;
    
    /* 添加轻微的动画效果 */
    animation: float 3s ease-in-out infinite;
    
    @keyframes float {
      0%, 100% {
        transform: translateY(0px);
      }
      50% {
        transform: translateY(-10px);
      }
    }
  }
  
  .empty-title {
    font-size: ${({ size }) => {
      switch (size) {
        case 'small':
          return theme.typography.fontSize.lg;
        case 'large':
          return theme.typography.fontSize['3xl'];
        default:
          return theme.typography.fontSize['2xl'];
      }
    }};
    font-weight: ${theme.typography.fontWeight.bold};
    color: ${theme.colors.gray[700]};
    margin-bottom: ${theme.spacing[3]};
  }
  
  .empty-description {
    font-size: ${({ size }) => {
      switch (size) {
        case 'small':
          return theme.typography.fontSize.sm;
        case 'large':
          return theme.typography.fontSize.lg;
        default:
          return theme.typography.fontSize.base;
      }
    }};
    color: ${theme.colors.gray[500]};
    margin-bottom: ${theme.spacing[8]};
    line-height: ${theme.typography.lineHeight.relaxed};
    max-width: 400px;
  }
  
  .empty-action {
    margin-top: ${theme.spacing[4]};
  }
`;

const EmptyState: React.FC<EmptyStateProps> = ({
  variant = 'no-data',
  title,
  description,
  actionText,
  onAction,
  showAction = true,
  size = 'medium'
}) => {
  const getEmptyConfig = () => {
    switch (variant) {
      case 'no-books':
        return {
          icon: <BookOutlined className="empty-icon" />,
          title: title || '暂无图书',
          description: description || '还没有图书数据，请稍后再试或联系管理员添加图书。',
          actionText: actionText || '刷新页面',
        };
      
      case 'no-search-results':
        return {
          icon: <SearchOutlined className="empty-icon" />,
          title: title || '未找到相关图书',
          description: description || '没有找到符合搜索条件的图书，请尝试调整搜索关键词或筛选条件。',
          actionText: actionText || '清空筛选',
        };
      
      case 'no-favorites':
        return {
          icon: <HeartOutlined className="empty-icon" />,
          title: title || '暂无收藏',
          description: description || '您还没有收藏任何图书，快去发现喜欢的图书并收藏吧！',
          actionText: actionText || '去逛逛',
        };
      
      case 'no-orders':
        return {
          icon: <ShoppingOutlined className="empty-icon" />,
          title: title || '暂无订单',
          description: description || '您还没有任何订单记录，快去选购心仪的图书吧！',
          actionText: actionText || '去购物',
        };
      
      case 'no-cart':
        return {
          icon: <InboxOutlined className="empty-icon" />,
          title: title || '购物车为空',
          description: description || '您的购物车还是空的，快去添加一些喜欢的图书吧！',
          actionText: actionText || '去选购',
        };
      
      case 'no-users':
        return {
          icon: <UserOutlined className="empty-icon" />,
          title: title || '暂无用户',
          description: description || '还没有用户数据。',
          actionText: actionText || '刷新',
        };
      
      case 'error':
        return {
          icon: <WarningOutlined className="empty-icon" style={{ color: theme.colors.error[400] }} />,
          title: title || '加载失败',
          description: description || '数据加载失败，请检查网络连接或稍后重试。',
          actionText: actionText || '重新加载',
        };
      
      default:
        return {
          icon: <FileSearchOutlined className="empty-icon" />,
          title: title || '暂无数据',
          description: description || '当前没有可显示的数据。',
          actionText: actionText || '刷新',
        };
    }
  };

  const config = getEmptyConfig();

  return (
    <EmptyContainer size={size}>
      {config.icon}
      <div className="empty-title">{config.title}</div>
      <div className="empty-description">{config.description}</div>
      {showAction && onAction && (
        <div className="empty-action">
          <Button
            variant="gradient"
            gradient="primary"
            size={size === 'small' ? 'small' : 'medium'}
            onClick={onAction}
            rounded
            elevated
          >
            {config.actionText}
          </Button>
        </div>
      )}
    </EmptyContainer>
  );
};

export default EmptyState;
